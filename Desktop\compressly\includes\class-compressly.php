<?php
/**
 * Main Compressly Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Compressly {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = COMPRESSLY_VERSION;
    
    /**
     * Admin instance
     */
    public $admin;
    
    /**
     * Settings instance
     */
    public $settings;
    
    /**
     * Compression engine
     */
    public $compression;
    
    /**
     * Get plugin instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_components();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Initialize admin
        if (is_admin()) {
            $this->admin = new Compressly_Admin();
        }
        
        // Initialize settings
        $this->settings = new Compressly_Settings();
        
        // Initialize compression engine
        $this->compression = new Compressly_Compression();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Hook into WordPress
        do_action('compressly_init');
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('compressly', false, dirname(COMPRESSLY_PLUGIN_BASENAME) . '/languages/');
    }
    
    /**
     * Plugin activation
     */
    public static function activate() {
        // Create database tables
        self::create_tables();
        
        // Set default options
        self::set_default_options();
        
        // Schedule cron jobs
        self::schedule_cron_jobs();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Clear scheduled cron jobs
        wp_clear_scheduled_hook('compressly_cleanup');
        wp_clear_scheduled_hook('compressly_stats_update');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        self::remove_tables();
        
        // Remove options
        self::remove_options();
        
        // Clear cron jobs
        wp_clear_scheduled_hook('compressly_cleanup');
        wp_clear_scheduled_hook('compressly_stats_update');
    }
    
    /**
     * Create database tables
     */
    private static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Optimization history table
        $table_name = $wpdb->prefix . 'compressly_optimizations';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) NOT NULL,
            original_size bigint(20) NOT NULL,
            optimized_size bigint(20) NOT NULL,
            compression_ratio decimal(5,2) NOT NULL,
            optimization_type varchar(50) NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY attachment_id (attachment_id),
            KEY status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default options
     */
    private static function set_default_options() {
        $default_options = array(
            'compression_quality' => 85,
            'auto_optimize' => true,
            'webp_conversion' => true,
            'lazy_loading' => true,
            'cdn_enabled' => false,
            'backup_originals' => true,
            'max_width' => 1920,
            'max_height' => 1080
        );
        
        add_option('compressly_settings', $default_options);
        add_option('compressly_version', COMPRESSLY_VERSION);
    }
    
    /**
     * Schedule cron jobs
     */
    private static function schedule_cron_jobs() {
        if (!wp_next_scheduled('compressly_cleanup')) {
            wp_schedule_event(time(), 'daily', 'compressly_cleanup');
        }
        
        if (!wp_next_scheduled('compressly_stats_update')) {
            wp_schedule_event(time(), 'hourly', 'compressly_stats_update');
        }
    }
    
    /**
     * Remove database tables
     */
    private static function remove_tables() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}compressly_optimizations");
    }
    
    /**
     * Remove options
     */
    private static function remove_options() {
        delete_option('compressly_settings');
        delete_option('compressly_version');
    }
}