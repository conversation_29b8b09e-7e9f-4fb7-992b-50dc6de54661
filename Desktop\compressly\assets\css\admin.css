/* Compressly Admin Styles */
.compressly-admin {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.compressly-logo img {
    vertical-align: middle;
    margin-right: 10px;
}

/* Dashboard Styles */
.compressly-dashboard {
    margin-top: 20px;
}

.compressly-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.compressly-stat-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.compressly-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-icon {
    margin-right: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    color: white;
    font-size: 20px;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
}

/* Progress Bar Styles */
.compressly-progress-bar {
    width: 100%;
    height: 20px;
    background: #f1f1f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.compressly-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Log Styles */
.compressly-log-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f9f9f9;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.compressly-log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-success {
    color: #46b450;
}

.log-error {
    color: #dc3232;
}

.log-warning {
    color: #ffb900;
}

/* Button Styles */
.compressly-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.compressly-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.compressly-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Indicators */
.status-ok {
    color: #46b450;
    font-weight: 600;
}

.status-warning {
    color: #ffb900;
    font-weight: 600;
}

.status-error {
    color: #dc3232;
    font-weight: 600;
}

/* Card Styles */
.compressly-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.compressly-card h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #f1f1f1;
    padding-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .compressly-stats-grid {
        grid-template-columns: 1fr;
    }

    .compressly-stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}