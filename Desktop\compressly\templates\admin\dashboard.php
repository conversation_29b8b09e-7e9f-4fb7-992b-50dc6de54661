<?php
/**
 * Admin Dashboard Template
 */

<?php if (!defined('ABSPATH')) exit; ?>
?>

<div class="wrap compressly-dashboard">
    <h1 class="wp-heading-inline">
        <?php echo esc_html(get_admin_page_title()); ?>
        <span class="compressly-version">v<?php echo COMPRESSLY_VERSION; ?></span>
    </h1>
    
    <div class="compressly-dashboard-grid">
        <!-- Stats Cards -->
        <div class="compressly-stats-cards">
            <div class="compressly-card compressly-stat-card">
                <div class="compressly-stat-icon">
                    <span class="dashicons dashicons-images-alt2"></span>
                </div>
                <div class="compressly-stat-content">
                    <h3><?php echo number_format($stats->total_optimized ?? 0); ?></h3>
                    <p><?php _e('Images Optimized', 'compressly'); ?></p>
                </div>
            </div>
            
            <div class="compressly-card compressly-stat-card">
                <div class="compressly-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="compressly-stat-content">
                    <h3><?php echo round($stats->avg_compression_ratio ?? 0, 1); ?>%</h3>
                    <p><?php _e('Average Compression', 'compressly'); ?></p>
                </div>
            </div>
            
            <div class="compressly-card compressly-stat-card">
                <div class="compressly-stat-icon">
                    <span class="dashicons dashicons-download"></span>
                </div>
                <div class="compressly-stat-content">
                    <h3><?php echo size_format(($stats->total_original_size ?? 0) - ($stats->total_optimized_size ?? 0)); ?></h3>
                    <p><?php _e('Space Saved', 'compressly'); ?></p>
                </div>
            </div>
            
            <div class="compressly-card compressly-stat-card">
                <div class="compressly-stat-icon">
                    <span class="dashicons dashicons-performance"></span>
                </div>
                <div class="compressly-stat-content">
                    <h3><?php echo round((($stats->total_original_size ?? 1) - ($stats->total_optimized_size ?? 0)) / ($stats->total_original_size ?? 1) * 100, 1); ?>%</h3>
                    <p><?php _e('Total Reduction', 'compressly'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="compressly-quick-actions">
            <div class="compressly-card">
                <h2><?php _e('Quick Actions', 'compressly'); ?></h2>
                
                <div class="compressly-action-buttons">
                    <a href="<?php echo admin_url('admin.php?page=compressly-bulk'); ?>" class="button button-primary button-large">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Bulk Optimize', 'compressly'); ?>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=compressly-settings'); ?>" class="button button-secondary button-large">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Settings', 'compressly'); ?>
                    </a>
                    
                    <button id="compressly-scan-media" class="button button-secondary button-large">
                        <span class="dashicons dashicons-search"></span>
                        <?php _e('Scan Media Library', 'compressly'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="compressly-recent-activity">
            <div class="compressly-card">
                <h2><?php _e('Recent Optimizations', 'compressly'); ?></h2>
                
                <div class="compressly-activity-list">
                    <?php
                    global $wpdb;
                    $recent_optimizations = $wpdb->get_results(
                        "SELECT o.*, p.post_title 
                         FROM {$wpdb->prefix}compressly_optimizations o 
                         LEFT JOIN {$wpdb->posts} p ON o.attachment_id = p.ID 
                         WHERE o.status = 'completed' 
                         ORDER BY o.created_at DESC 
                         LIMIT 10"
                    );
                    
                    if ($recent_optimizations) :
                        foreach ($recent_optimizations as $optimization) :
                    ?>
                    <div class="compressly-activity-item">
                        <div class="compressly-activity-icon">
                            <span class="dashicons dashicons-yes-alt"></span>
                        </div>
                        <div class="compressly-activity-content">
                            <strong><?php echo esc_html($optimization->post_title); ?></strong>
                            <span class="compressly-activity-meta">
                                <?php printf(
                                    __('Saved %s (%s%%) - %s ago', 'compressly'),
                                    size_format($optimization->original_size - $optimization->optimized_size),
                                    round($optimization->compression_ratio, 1),
                                    human_time_diff(strtotime($optimization->created_at))
                                ); ?>
                            </span>
                        </div>
                    </div>
                    <?php 
                        endforeach;
                    else :
                    ?>
                    <div class="compressly-empty-state">
                        <p><?php _e('No optimizations yet. Start by optimizing your media library!', 'compressly'); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="compressly-system-status">
            <div class="compressly-card">
                <h2><?php _e('System Status', 'compressly'); ?></h2>
                
                <div class="compressly-status-list">
                    <?php
                    // Add error checking for system requirements
                    $gd_available = extension_loaded('gd');
                    $webp_support = function_exists('imagewebp');
                    $memory_limit = ini_get('memory_limit');
                    $max_upload = wp_max_upload_size();

                    // Convert memory limit to bytes for comparison
                    $memory_bytes = wp_convert_hr_to_bytes($memory_limit);
                    $recommended_memory = 128 * 1024 * 1024; // 128MB
                    ?>

                    <div class="compressly-status-item">
                        <span class="compressly-status-label"><?php _e('GD Library', 'compressly'); ?></span>
                        <span class="compressly-status-value <?php echo $gd_available ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $gd_available ? __('Available', 'compressly') : __('Not Available', 'compressly'); ?>
                        </span>
                        <?php if (!$gd_available): ?>
                            <small class="description"><?php _e('GD Library is required for image optimization', 'compressly'); ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <div class="compressly-status-item">
                        <span class="compressly-status-label"><?php _e('WebP Support', 'compressly'); ?></span>
                        <span class="compressly-status-value <?php echo $webp_support ? 'status-ok' : 'status-warning'; ?>">
                            <?php echo $webp_support ? __('Available', 'compressly') : __('Not Available', 'compressly'); ?>
                        </span>
                        <?php if (!$webp_support): ?>
                            <small class="description"><?php _e('WebP support recommended for better compression', 'compressly'); ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <div class="compressly-status-item">
                        <span class="compressly-status-label"><?php _e('Memory Limit', 'compressly'); ?></span>
                        <span class="compressly-status-value <?php echo $memory_bytes >= $recommended_memory ? 'status-ok' : 'status-warning'; ?>">
                            <?php echo $memory_limit; ?>
                        </span>
                        <?php if ($memory_bytes < $recommended_memory): ?>
                            <small class="description"><?php _e('128MB+ recommended for large image processing', 'compressly'); ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <div class="compressly-status-item">
                        <span class="compressly-status-label"><?php _e('Max Upload Size', 'compressly'); ?></span>
                        <span class="compressly-status-value"><?php echo size_format($max_upload); ?></span>
                    </div>
                </div>            </div>
        </div>
    </div>
</div>