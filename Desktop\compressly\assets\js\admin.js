(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initDashboard();
        initBulkOptimizer();
        initSettings();
    });

    /**
     * Initialize Dashboard
     */
    function initDashboard() {
        // Auto-refresh stats every 30 seconds
        if ($('.compressly-dashboard').length) {
            setInterval(refreshStats, 30000);
        }

        // Scan media library button
        $('#compressly-scan-media').on('click', function() {
            scanMediaLibrary();
        });
    }

    /**
     * Initialize Settings
     */
    function initSettings() {
        // Quality slider with live preview
        $('.compressly-range-slider').on('input', function() {
            var value = $(this).val();
            $(this).siblings('.compressly-range-value').text(value + '%');
        });

        // Reset settings confirmation
        $('#reset-settings').on('click', function() {
            if (confirm(compressly_ajax.strings.reset_confirm || 'Are you sure you want to reset all settings to defaults?')) {
                // Reset form to defaults
                $('#compression_quality').val(85).trigger('input');
                $('input[type="checkbox"]').prop('checked', true);
                $('input[name="avif_conversion"]').prop('checked', false);
                $('input[name="max_width"]').val(1920);
                $('input[name="max_height"]').val(1080);
            }
        });
    }

    /**
     * Initialize Bulk Optimizer
     */
    function initBulkOptimizer() {
        let isOptimizing = false;
        let currentOffset = 0;
        let totalImages = 0;
        let processedImages = 0;
        let totalSaved = 0;

        // Get total images count
        totalImages = parseInt($('#total-images').text()) || 0;

        // Start bulk optimization
        $('#start-bulk-optimization').on('click', function() {
            if (isOptimizing) return;

            isOptimizing = true;
            currentOffset = 0;
            processedImages = 0;
            totalSaved = 0;

            $(this).hide();
            $('#pause-bulk-optimization, #stop-bulk-optimization').show();
            $('#progress-text').text(compressly_ajax.strings.optimizing || 'Starting optimization...');

            processBatch();
        });

        // Pause bulk optimization
        $('#pause-bulk-optimization').on('click', function() {
            isOptimizing = false;
            $(this).hide();
            $('#start-bulk-optimization').show().text('Resume Optimization');
        });

        // Stop bulk optimization
        $('#stop-bulk-optimization').on('click', function() {
            isOptimizing = false;
            resetUI();
        });

        /**
         * Process a batch of images
         */
        function processBatch() {
            if (!isOptimizing) return;

            $.ajax({
                url: compressly_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'compressly_bulk_optimize',
                    nonce: compressly_ajax.nonce,
                    offset: currentOffset,
                    limit: 5 // Process 5 images at a time
                },
                timeout: 30000,
                success: function(response) {
                    console.log('Bulk optimization response:', response);

                    if (response.success && response.data && response.data.results) {
                        // Process results
                        response.data.results.forEach(function(result) {
                            processedImages++;

                            if (result.success) {
                                if (result.original_size && result.optimized_size) {
                                    totalSaved += (result.original_size - result.optimized_size);
                                }
                                addLogEntry(result.message, 'success');
                            } else {
                                addLogEntry(result.message, 'error');
                            }
                        });

                        // Update progress
                        updateProgress();

                        // Continue if there are more images
                        if (response.data.has_more && isOptimizing) {
                            currentOffset += response.data.processed;
                            setTimeout(processBatch, 1000); // Small delay between batches
                        } else {
                            // Optimization complete
                            completeOptimization();
                        }
                    } else {
                        addLogEntry(response.data?.message || 'Error processing batch', 'error');
                        completeOptimization();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    addLogEntry(compressly_ajax.strings.network_error || 'Network error occurred', 'error');
                    completeOptimization();
                }
            });
        }

        /**
         * Update progress display
         */
        function updateProgress() {
            if (totalImages > 0) {
                const progress = Math.min((processedImages / totalImages) * 100, 100);
                $('#progress-fill').css('width', progress + '%');
                $('#progress-percentage').text(Math.round(progress) + '%');
                $('#optimized-count').text(processedImages);
                $('#space-saved').text(formatBytes(totalSaved));
                $('#progress-text').text(processedImages + ' / ' + totalImages + ' images processed');
            }
        }

        /**
         * Complete optimization process
         */
        function completeOptimization() {
            isOptimizing = false;
            resetUI();
            addLogEntry(compressly_ajax.strings.completed || 'Optimization completed!', 'success');
        }

        /**
         * Reset UI to initial state
         */
        function resetUI() {
            $('#start-bulk-optimization').show().text('Start Optimization');
            $('#pause-bulk-optimization, #stop-bulk-optimization').hide();
        }

        /**
         * Add log entry
         */
        function addLogEntry(message, type) {
            const logContainer = $('#optimization-log');
            if (logContainer.length) {
                const timestamp = new Date().toLocaleTimeString();
                const entry = $('<div>').addClass('compressly-log-entry log-' + type)
                    .text(timestamp + ': ' + message);

                logContainer.append(entry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Remove empty state message
                logContainer.find('.compressly-log-empty').remove();
            }
        }
    }

    /**
     * Refresh dashboard stats
     */
    function refreshStats() {
        // This would make an AJAX call to refresh stats
        // Implementation depends on specific requirements
    }

    /**
     * Scan media library
     */
    function scanMediaLibrary() {
        // This would scan the media library for unoptimized images
        // Implementation depends on specific requirements
    }

    /**
     * Format bytes to human readable format
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

})(jQuery);