<?php
/**
 * Settings Template
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap compressly-settings">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <form method="post" action="">
        <?php wp_nonce_field('compressly_save_settings', 'compressly_settings_nonce'); ?>

        <div class="compressly-settings-grid">
            <!-- General Settings -->
            <div class="compressly-card">
                <h2><?php _e('General Settings', 'compressly'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Compression Quality', 'compressly'); ?></th>
                        <td>
                            <input type="range" name="compression_quality" id="compression_quality"
                                   min="1" max="100" value="<?php echo esc_attr($settings['compression_quality'] ?? 85); ?>"
                                   class="compressly-range-slider">
                            <span class="compressly-range-value" id="quality-value"><?php echo esc_attr($settings['compression_quality'] ?? 85); ?>%</span>
                            <div class="compressly-quality-presets" style="margin: 10px 0;">
                                <button type="button" class="button" onclick="setQuality(75)">Small Files (75%)</button>
                                <button type="button" class="button button-primary" onclick="setQuality(85)">Balanced (85%)</button>
                                <button type="button" class="button" onclick="setQuality(95)">High Quality (95%)</button>
                            </div>
                            <p class="description"><?php _e('Recommended: 85% for balanced quality/size, 75% for smaller files, 95% for high quality.', 'compressly'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Auto Optimization', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_optimize" value="1"
                                       <?php checked($settings['auto_optimize'] ?? true); ?>>
                                <?php _e('Automatically optimize images on upload', 'compressly'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Backup Originals', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="backup_originals" value="1"
                                       <?php checked($settings['backup_originals'] ?? true); ?>>
                                <?php _e('Keep backup copies of original images', 'compressly'); ?>
                            </label>
                            <p class="description"><?php _e('Recommended for safety. Allows you to restore original images if needed.', 'compressly'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Format Settings -->
            <div class="compressly-card">
                <h2><?php _e('Format Settings', 'compressly'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('WebP Conversion', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="webp_conversion" value="1"
                                       <?php checked($settings['webp_conversion'] ?? true); ?>
                                       <?php disabled(!function_exists('imagewebp')); ?>>
                                <?php _e('Generate WebP versions of images', 'compressly'); ?>
                            </label>
                            <?php if (!function_exists('imagewebp')) : ?>
                            <p class="description compressly-warning"><?php _e('WebP support is not available on your server.', 'compressly'); ?></p>
                            <?php endif; ?>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('AVIF Conversion', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="avif_conversion" value="1"
                                       <?php checked($settings['avif_conversion'] ?? false); ?>>
                                <?php _e('Generate AVIF versions of images (experimental)', 'compressly'); ?>
                            </label>
                            <p class="description"><?php _e('AVIF provides better compression than WebP but has limited browser support.', 'compressly'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Resize Settings -->
            <div class="compressly-card">
                <h2><?php _e('Resize Settings', 'compressly'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Resize Large Images', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="resize_larger_images" value="1"
                                       <?php checked($settings['resize_larger_images'] ?? true); ?>>
                                <?php _e('Automatically resize images larger than specified dimensions', 'compressly'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Maximum Width', 'compressly'); ?></th>
                        <td>
                            <input type="number" name="max_width" value="<?php echo esc_attr($settings['max_width'] ?? 1920); ?>"
                                   min="100" max="5000" class="small-text"> px
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Maximum Height', 'compressly'); ?></th>
                        <td>
                            <input type="number" name="max_height" value="<?php echo esc_attr($settings['max_height'] ?? 1080); ?>"
                                   min="100" max="5000" class="small-text"> px
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Advanced Settings -->
            <div class="compressly-card">
                <h2><?php _e('Advanced Settings', 'compressly'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Lazy Loading', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="lazy_loading" value="1"
                                       <?php checked($settings['lazy_loading'] ?? true); ?>>
                                <?php _e('Enable lazy loading for images', 'compressly'); ?>
                            </label>
                            <p class="description"><?php _e('Images will load only when they come into view, improving page speed.', 'compressly'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Strip Metadata', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="strip_metadata" value="1"
                                       <?php checked($settings['strip_metadata'] ?? true); ?>>
                                <?php _e('Remove EXIF and other metadata from images', 'compressly'); ?>
                            </label>
                            <p class="description"><?php _e('Reduces file size by removing camera settings, GPS data, etc.', 'compressly'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Progressive JPEG', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="progressive_jpeg" value="1"
                                       <?php checked($settings['progressive_jpeg'] ?? true); ?>>
                                <?php _e('Create progressive JPEG images', 'compressly'); ?>
                            </label>
                            <p class="description"><?php _e('Progressive JPEGs load incrementally, appearing to load faster.', 'compressly'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary" value="<?php _e('Save Settings', 'compressly'); ?>">
            <button type="button" id="reset-settings" class="button button-secondary"><?php _e('Reset to Defaults', 'compressly'); ?></button>
        </p>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Range slider updates
    $('#compression_quality').on('input', function() {
        $('#quality-value').text($(this).val() + '%');
    });

    // Quality preset function
    window.setQuality = function(quality) {
        $('#compression_quality').val(quality).trigger('input');
    };

    // Reset settings confirmation
    $('#reset-settings').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to reset all settings to defaults?', 'compressly'); ?>')) {
            // Reset form to defaults
            $('#compression_quality').val(85).trigger('input');
            $('input[type="checkbox"]').prop('checked', true);
            $('input[name="avif_conversion"]').prop('checked', false);
            $('input[name="max_width"]').val(1920);
            $('input[name="max_height"]').val(1080);
        }
    });
});
</script>