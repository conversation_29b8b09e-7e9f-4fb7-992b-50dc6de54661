<?php
/**
 * Bulk Optimizer Template
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap compressly-bulk-optimizer">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <div class="compressly-bulk-container">
        <div class="compressly-card">
            <div class="compressly-bulk-header">
                <h2><?php _e('Bulk Image Optimization', 'compressly'); ?></h2>
                <p><?php _e('Optimize all unoptimized images in your media library with one click.', 'compressly'); ?></p>
            </div>

            <div class="compressly-bulk-stats">
                <div class="compressly-bulk-stat">
                    <span class="compressly-bulk-number" id="total-images"><?php echo count($unoptimized_images); ?></span>
                    <span class="compressly-bulk-label"><?php _e('Images to Optimize', 'compressly'); ?></span>
                </div>

                <div class="compressly-bulk-stat">
                    <span class="compressly-bulk-number" id="optimized-count">0</span>
                    <span class="compressly-bulk-label"><?php _e('Optimized', 'compressly'); ?></span>
                </div>

                <div class="compressly-bulk-stat">
                    <span class="compressly-bulk-number" id="space-saved">0 KB</span>
                    <span class="compressly-bulk-label"><?php _e('Space Saved', 'compressly'); ?></span>
                </div>
            </div>

            <div class="compressly-bulk-progress">
                <div class="compressly-progress-bar">
                    <div class="compressly-progress-fill" id="progress-fill" style="width: 0%;"></div>
                </div>
                <div class="compressly-progress-text">
                    <span id="progress-text"><?php _e('Ready to start', 'compressly'); ?></span>
                    <span id="progress-percentage">0%</span>
                </div>
            </div>

            <div class="compressly-bulk-controls">
                <button id="start-bulk-optimization" class="button button-primary button-large" <?php echo empty($unoptimized_images) ? 'disabled' : ''; ?>>
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Start Optimization', 'compressly'); ?>
                </button>

                <button id="pause-bulk-optimization" class="button button-secondary" style="display: none;">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <?php _e('Pause', 'compressly'); ?>
                </button>

                <button id="stop-bulk-optimization" class="button button-secondary" style="display: none;">
                    <span class="dashicons dashicons-no"></span>
                    <?php _e('Stop', 'compressly'); ?>
                </button>
            </div>

            <?php if (empty($unoptimized_images)) : ?>
            <div class="compressly-empty-state">
                <div class="compressly-empty-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <h3><?php _e('All images are optimized!', 'compressly'); ?></h3>
                <p><?php _e('Great job! All images in your media library have been optimized.', 'compressly'); ?></p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Optimization Log -->
        <div class="compressly-card compressly-optimization-log">
            <h3><?php _e('Optimization Log', 'compressly'); ?></h3>
            <div id="optimization-log" class="compressly-log-container">
                <p class="compressly-log-empty"><?php _e('Optimization log will appear here...', 'compressly'); ?></p>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    let isOptimizing = false;
    let isPaused = false;
    let currentOffset = 0;
    let totalImages = parseInt($('#total-images').text());
    let optimizedCount = 0;
    let totalSpaceSaved = 0;

    $('#start-bulk-optimization').on('click', function() {
        if (!isOptimizing) {
            startBulkOptimization();
        }
    });

    $('#pause-bulk-optimization').on('click', function() {
        isPaused = !isPaused;
        $(this).find('.dashicons').toggleClass('dashicons-controls-pause dashicons-controls-play');
        $(this).find('span:not(.dashicons)').text(isPaused ? '<?php _e('Resume', 'compressly'); ?>' : '<?php _e('Pause', 'compressly'); ?>');
    });

    $('#stop-bulk-optimization').on('click', function() {
        isOptimizing = false;
        isPaused = false;
        resetUI();
    });

    function startBulkOptimization() {
        isOptimizing = true;
        $('#start-bulk-optimization').hide();
        $('#pause-bulk-optimization, #stop-bulk-optimization').show();
        $('#progress-text').text('<?php _e('Starting optimization...', 'compressly'); ?>');

        processNextBatch();
    }

    function processNextBatch() {
        if (!isOptimizing) return;

        if (isPaused) {
            setTimeout(processNextBatch, 1000);
            return;
        }

        $.ajax({
            url: compressly_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'compressly_bulk_optimize',
                nonce: compressly_ajax.nonce,
                offset: currentOffset,
                limit: 10 // Add batch size limit
            },
            success: function(response) {
                if (response.success && response.data) {
                    if (response.data.results && Array.isArray(response.data.results)) {
                        response.data.results.forEach(function(result) {
                            if (result.success) {
                                optimizedCount++;
                                if (result.original_size && result.optimized_size) {
                                    totalSpaceSaved += (result.original_size - result.optimized_size);
                                }
                                addLogEntry(result.message || 'Image optimized successfully', 'success');
                            } else {
                                addLogEntry(result.message || 'Failed to optimize image', 'error');
                            }
                        });

                        currentOffset += response.data.results.length;
                        updateProgress();

                        if (response.data.has_more && isOptimizing) {
                            setTimeout(processNextBatch, 500);
                        } else {
                            completeOptimization();
                        }
                    } else {
                        addLogEntry('<?php _e('Invalid response format', 'compressly'); ?>', 'error');
                        completeOptimization();
                    }
                } else {
                    addLogEntry(response.data?.message || '<?php _e('Error occurred during optimization', 'compressly'); ?>', 'error');
                    completeOptimization();
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                addLogEntry('<?php _e('Network error occurred', 'compressly'); ?>', 'error');
                completeOptimization();
            },
            timeout: 30000 // Add timeout
        });
    }

    function updateProgress() {
        const percentage = Math.min((optimizedCount / totalImages) * 100, 100);
        $('#progress-fill').css('width', percentage + '%');
        $('#progress-percentage').text(Math.round(percentage) + '%');
        $('#optimized-count').text(optimizedCount);
        $('#space-saved').text(formatBytes(totalSpaceSaved));
        $('#progress-text').text(`${optimizedCount} / ${totalImages} images optimized`);
    }

    function completeOptimization() {
        isOptimizing = false;
        resetUI();
        addLogEntry(`Optimization completed! ${optimizedCount} images optimized, ${formatBytes(totalSpaceSaved)} saved.`, 'success');
    }

    function resetUI() {
        $('#start-bulk-optimization').show();
        $('#pause-bulk-optimization, #stop-bulk-optimization').hide();
    }

    function addLogEntry(message, type) {
        const logContainer = $('#optimization-log');
        if (logContainer.length) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = $('<div>').addClass(`compressly-log-entry log-${type}`)
                .text(timestamp + ': ' + message);

            logContainer.append(entry);
            logContainer.scrollTop(logContainer[0].scrollHeight);

            // Remove empty state message
            logContainer.find('.compressly-log-empty').remove();
        }
    }

    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>