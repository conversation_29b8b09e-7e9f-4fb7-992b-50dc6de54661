<?php
declare(strict_types=1);

/**
 * Compressly Admin Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Compressly_Admin {

    private $compression_engine;

    public function __construct() {
        // Initialize compression engine
        if (class_exists('Compressly_Compression')) {
            $this->compression_engine = new Compressly_Compression();
        }

        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_compressly_bulk_optimize', array($this, 'handle_bulk_optimize'));
        add_action('wp_ajax_compressly_reset_optimization', array($this, 'handle_reset_optimization'));
    }

    public function add_admin_menu() {
        add_menu_page(
            __('Compressly', 'compressly'),
            __('Compressly', 'compressly'),
            'manage_options',
            'compressly',
            array($this, 'dashboard_page'),
            'dashicons-images-alt2',
            30
        );

        add_submenu_page(
            'compressly',
            __('Bulk Optimizer', 'compressly'),
            __('Bulk Optimizer', 'compressly'),
            'manage_options',
            'compressly-bulk',
            array($this, 'bulk_optimizer_page')
        );

        add_submenu_page(
            'compressly',
            __('Settings', 'compressly'),
            __('Settings', 'compressly'),
            'manage_options',
            'compressly-settings',
            array($this, 'settings_page')
        );
    }

    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'compressly') === false) {
            return;
        }

        // Create assets directory if it doesn't exist
        $css_file = COMPRESSLY_PLUGIN_URL . 'assets/css/admin.css';
        $js_file = COMPRESSLY_PLUGIN_URL . 'assets/js/admin.js';

        wp_enqueue_style(
            'compressly-admin',
            $css_file,
            array(),
            COMPRESSLY_VERSION
        );

        wp_enqueue_script(
            'compressly-admin',
            $js_file,
            array('jquery'),
            COMPRESSLY_VERSION,
            true
        );

        wp_localize_script('compressly-admin', 'compressly_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('compressly_nonce'),
            'strings' => array(
                'optimizing' => __('Optimizing...', 'compressly'),
                'completed' => __('Optimization completed!', 'compressly'),
                'error' => __('Error occurred during optimization', 'compressly'),
                'network_error' => __('Network error occurred', 'compressly'),
                'invalid_response' => __('Invalid response format', 'compressly'),
            )
        ));
    }

    public function dashboard_page() {
        $stats = $this->get_optimization_stats();
        $system_status = $this->get_system_status();

        // Include template if it exists, otherwise show inline
        $template_file = COMPRESSLY_PLUGIN_DIR . 'templates/admin/dashboard.php';
        if (file_exists($template_file)) {
            include $template_file;
        } else {
            $this->render_dashboard_inline($stats, $system_status);
        }
    }

    public function bulk_optimizer_page() {
        $unoptimized_count = $this->get_unoptimized_count();
        $unoptimized_images = $this->get_unoptimized_images(); // Get actual images for template

        // Include template if it exists, otherwise show inline
        $template_file = COMPRESSLY_PLUGIN_DIR . 'templates/admin/bulk-optimizer.php';
        if (file_exists($template_file)) {
            include $template_file;
        } else {
            $this->render_bulk_optimizer_inline($unoptimized_count);
        }
    }

    public function settings_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['compressly_settings_nonce'] ?? '', 'compressly_save_settings')) {
            $this->save_settings();
        }

        // Get current settings
        $settings = get_option('compressly_settings', array());

        // Include template if it exists, otherwise show basic form
        $template_file = COMPRESSLY_PLUGIN_DIR . 'templates/admin/settings.php';
        if (file_exists($template_file)) {
            include $template_file;
        } else {
            $this->render_settings_inline($settings);
        }
    }

    private function save_settings() {
        $settings = array(
            'compression_quality' => max(1, min(100, intval($_POST['compression_quality'] ?? 85))),
            'auto_optimize' => isset($_POST['auto_optimize']),
            'backup_originals' => isset($_POST['backup_originals']),
            'webp_conversion' => isset($_POST['webp_conversion']),
            'avif_conversion' => isset($_POST['avif_conversion']),
            'resize_larger_images' => isset($_POST['resize_larger_images']),
            'max_width' => max(100, min(5000, intval($_POST['max_width'] ?? 1920))),
            'max_height' => max(100, min(5000, intval($_POST['max_height'] ?? 1080))),
            'lazy_loading' => isset($_POST['lazy_loading']),
            'strip_metadata' => isset($_POST['strip_metadata']),
            'progressive_jpeg' => isset($_POST['progressive_jpeg'])
        );

        update_option('compressly_settings', $settings);

        // Show success message
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'compressly') . '</p></div>';
        });
    }

    private function render_settings_inline($settings) {
        ?>
        <div class="wrap">
            <h1><?php _e('Compressly Settings', 'compressly'); ?></h1>
            <form method="post" action="">
                <?php wp_nonce_field('compressly_save_settings', 'compressly_settings_nonce'); ?>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Compression Quality', 'compressly'); ?></th>
                        <td>
                            <input type="number" name="compression_quality" value="<?php echo esc_attr($settings['compression_quality'] ?? 85); ?>" min="1" max="100" />
                            <p class="description"><?php _e('Higher values mean better quality but larger file sizes.', 'compressly'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Auto Optimization', 'compressly'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_optimize" value="1" <?php checked($settings['auto_optimize'] ?? true); ?> />
                                <?php _e('Automatically optimize images on upload', 'compressly'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    private function render_dashboard_inline($stats, $system_status) {
        ?>
        <div class="wrap">
            <h1><?php _e('Compressly Dashboard', 'compressly'); ?></h1>

            <div class="compressly-stats">
                <h2><?php _e('Optimization Statistics', 'compressly'); ?></h2>
                <p><?php printf(__('Total Optimized: %d', 'compressly'), $stats['total_optimized']); ?></p>
                <p><?php printf(__('Total Saved: %s', 'compressly'), size_format($stats['total_saved'])); ?></p>
                <p><?php printf(__('Average Reduction: %s%%', 'compressly'), $stats['avg_reduction']); ?></p>
            </div>

            <div class="compressly-system-status">
                <h2><?php _e('System Status', 'compressly'); ?></h2>
                <p><?php _e('GD Library:', 'compressly'); ?>
                    <span class="<?php echo $system_status['gd_library'] ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $system_status['gd_library'] ? __('Available', 'compressly') : __('Not Available', 'compressly'); ?>
                    </span>
                </p>
                <p><?php _e('WebP Support:', 'compressly'); ?>
                    <span class="<?php echo $system_status['webp_support'] ? 'status-ok' : 'status-warning'; ?>">
                        <?php echo $system_status['webp_support'] ? __('Available', 'compressly') : __('Not Available', 'compressly'); ?>
                    </span>
                </p>
                <p><?php _e('Memory Limit:', 'compressly'); ?> <?php echo $system_status['memory_limit']; ?></p>
                <p><?php _e('Max Upload Size:', 'compressly'); ?> <?php echo $system_status['max_upload']; ?></p>
            </div>
        </div>

        <style>
        .status-ok { color: #46b450; }
        .status-warning { color: #ffb900; }
        .status-error { color: #dc3232; }
        .compressly-stats, .compressly-system-status {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        </style>
        <?php
    }

    private function render_bulk_optimizer_inline($unoptimized_count) {
        ?>
        <div class="wrap">
            <h1><?php _e('Bulk Optimizer', 'compressly'); ?></h1>

            <div class="compressly-bulk-optimizer">
                <p><?php printf(__('Found %d unoptimized images', 'compressly'), $unoptimized_count); ?></p>

                <button id="start-optimization" class="button button-primary">
                    <?php _e('Start Optimization', 'compressly'); ?>
                </button>

                <div id="optimization-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div id="optimization-log"></div>
                </div>
            </div>
        </div>

        <style>
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f1f1f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: #0073aa;
            transition: width 0.3s ease;
        }
        #optimization-log {
            max-height: 300px;
            overflow-y: auto;
            background: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('#start-optimization').click(function() {
                startOptimization();
            });

            function startOptimization() {
                $('#optimization-progress').show();
                processNextBatch(0);
            }

            function processNextBatch(offset) {
                $.ajax({
                    url: compressly_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'compressly_bulk_optimize',
                        nonce: compressly_ajax.nonce,
                        offset: offset,
                        limit: 10
                    },
                    success: function(response) {
                        if (response.success) {
                            response.data.results.forEach(function(result) {
                                addLogEntry(result.message, result.success ? 'success' : 'error');
                            });

                            if (response.data.has_more) {
                                setTimeout(function() {
                                    processNextBatch(offset + response.data.processed);
                                }, 500);
                            } else {
                                addLogEntry('Optimization completed!', 'success');
                            }
                        } else {
                            addLogEntry('Error: ' + response.data.message, 'error');
                        }
                    },
                    error: function() {
                        addLogEntry('Network error occurred', 'error');
                    }
                });
            }

            function addLogEntry(message, type) {
                var log = $('#optimization-log');
                var entry = $('<div>').text(new Date().toLocaleTimeString() + ': ' + message);
                entry.css('color', type === 'success' ? '#46b450' : '#dc3232');
                log.append(entry);
                log.scrollTop(log[0].scrollHeight);
            }
        });
        </script>
        <?php
    }

    public function handle_bulk_optimize() {
        // Log the request for debugging
        error_log('Compressly: Bulk optimize request received. POST data: ' . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'compressly_nonce')) {
            error_log('Compressly: Nonce verification failed');
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            error_log('Compressly: Permission check failed');
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $offset = intval($_POST['offset'] ?? 0);
        $limit = min(intval($_POST['limit'] ?? 5), 10); // Reduced batch size

        error_log("Compressly: Processing batch - Offset: $offset, Limit: $limit");

        try {
            $result = $this->process_bulk_optimization($offset, $limit);
            error_log('Compressly: Batch processed successfully. Result: ' . print_r($result, true));
            wp_send_json_success($result);
        } catch (Exception $e) {
            error_log('Compressly error: ' . $e->getMessage());
            error_log('Compressly error trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Optimization failed: ' . $e->getMessage()));
        }
    }

    private function process_bulk_optimization($offset, $limit) {
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => array('image/jpeg', 'image/png', 'image/gif'),
            'posts_per_page' => $limit,
            'offset' => $offset,
            'post_status' => 'inherit',
            'meta_query' => array(
                array(
                    'key' => '_compressly_optimized',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));

        error_log("Compressly: Found " . count($attachments) . " attachments to process");

        $results = array();

        foreach ($attachments as $attachment) {
            $file_path = get_attached_file($attachment->ID);

            error_log("Compressly: Processing attachment ID {$attachment->ID}, file: $file_path");

            if (!$file_path || !file_exists($file_path)) {
                error_log("Compressly: File not found for attachment ID {$attachment->ID}");
                $results[] = array(
                    'success' => false,
                    'message' => sprintf(__('File not found for %s', 'compressly'), $attachment->post_title ?: 'Unknown')
                );
                continue;
            }

            $original_size = filesize($file_path);
            error_log("Compressly: Original file size: $original_size bytes");

            // Check if image is already optimized
            if (get_post_meta($attachment->ID, '_compressly_optimized', true)) {
                error_log("Compressly: Image {$attachment->ID} already optimized, skipping");
                $results[] = array(
                    'success' => true,
                    'message' => sprintf(__('%s already optimized', 'compressly'), basename($file_path)),
                    'original_size' => $original_size,
                    'optimized_size' => $original_size
                );
                continue;
            }

            $optimization_result = $this->optimize_image($file_path, $attachment->ID);
            error_log("Compressly: Optimization result: " . print_r($optimization_result, true));

            if ($optimization_result['success']) {
                $optimized_size = filesize($file_path);
                $saved_bytes = max(0, $original_size - $optimized_size); // Ensure non-negative

                $results[] = array(
                    'success' => true,
                    'message' => sprintf(__('Optimized %s (saved %s)', 'compressly'), basename($file_path), size_format($saved_bytes)),
                    'original_size' => $original_size,
                    'optimized_size' => $optimized_size
                );

                update_post_meta($attachment->ID, '_compressly_optimized', time());
                $this->save_optimization_stats($attachment->ID, $original_size, $optimized_size);

                error_log("Compressly: Successfully optimized {$attachment->ID}, saved $saved_bytes bytes");
            } else {
                $results[] = array(
                    'success' => false,
                    'message' => sprintf(__('Failed to optimize %s: %s', 'compressly'), basename($file_path), $optimization_result['message'])
                );
                error_log("Compressly: Failed to optimize {$attachment->ID}: " . $optimization_result['message']);
            }
        }

        $total_unoptimized = $this->get_unoptimized_count();
        $has_more = ($offset + count($results)) < $total_unoptimized;

        error_log("Compressly: Batch complete. Processed: " . count($results) . ", Has more: " . ($has_more ? 'yes' : 'no'));

        return array(
            'results' => $results,
            'has_more' => $has_more,
            'processed' => count($results),
            'total_remaining' => $total_unoptimized - ($offset + count($results))
        );
    }

    private function optimize_image($file_path, $attachment_id) {
        try {
            error_log("Compressly: optimize_image called for attachment $attachment_id, file: $file_path");

            // Use compression engine if available
            if ($this->compression_engine && method_exists($this->compression_engine, 'optimize_image')) {
                error_log("Compressly: Using compression engine for optimization");
                $result = $this->compression_engine->optimize_image($attachment_id);
                error_log("Compressly: Compression engine result: " . print_r($result, true));
                return $result;
            }

            error_log("Compressly: Using fallback optimization");

            // Fallback to basic optimization
            $image_info = getimagesize($file_path);
            if (!$image_info) {
                error_log("Compressly: getimagesize failed for $file_path");
                return array('success' => false, 'message' => 'Invalid image');
            }

            $mime_type = $image_info['mime'];
            error_log("Compressly: Image mime type: $mime_type");

            switch ($mime_type) {
                case 'image/jpeg':
                    return $this->optimize_jpeg($file_path);
                case 'image/png':
                    return $this->optimize_png($file_path);
                case 'image/gif':
                    return array('success' => true, 'message' => 'GIF processed (no compression applied)');
                default:
                    return array('success' => false, 'message' => 'Unsupported format: ' . $mime_type);
            }
        } catch (Exception $e) {
            error_log('Compressly optimization error: ' . $e->getMessage());
            error_log('Compressly optimization error trace: ' . $e->getTraceAsString());
            return array('success' => false, 'message' => $e->getMessage());
        }
    }

    private function get_optimization_stats() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'compressly_stats';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return array(
                'total_optimized' => 0,
                'total_saved' => 0,
                'avg_reduction' => 0
            );
        }

        $stats = $wpdb->get_row("
            SELECT
                COUNT(*) as total_optimized,
                SUM(original_size - optimized_size) as total_saved,
                AVG((original_size - optimized_size) / original_size * 100) as avg_reduction
            FROM $table_name
        ");

        return array(
            'total_optimized' => intval($stats->total_optimized ?? 0),
            'total_saved' => intval($stats->total_saved ?? 0),
            'avg_reduction' => round(floatval($stats->avg_reduction ?? 0), 1)
        );
    }

    private function get_system_status() {
        return array(
            'gd_library' => extension_loaded('gd'),
            'webp_support' => function_exists('imagewebp'),
            'memory_limit' => ini_get('memory_limit'),
            'max_upload' => size_format(wp_max_upload_size())
        );
    }

    private function get_unoptimized_count() {
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => array('image/jpeg', 'image/png', 'image/gif'),
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'meta_query' => array(
                array(
                    'key' => '_compressly_optimized',
                    'compare' => 'NOT EXISTS'
                )
            ),
            'fields' => 'ids'
        ));

        return count($attachments);
    }

    private function get_unoptimized_images() {
        return get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => array('image/jpeg', 'image/png', 'image/gif'),
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'meta_query' => array(
                array(
                    'key' => '_compressly_optimized',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));
    }

    private function optimize_jpeg($file_path) {
        if (!function_exists('imagecreatefromjpeg')) {
            return array('success' => false, 'message' => 'GD library not available for JPEG');
        }

        // Get current settings or use defaults
        $settings = get_option('compressly_settings', array());
        $quality = intval($settings['compression_quality'] ?? 85);

        // Get original file size
        $original_size = filesize($file_path);

        $image = imagecreatefromjpeg($file_path);
        if (!$image) {
            return array('success' => false, 'message' => 'Failed to load JPEG image: ' . basename($file_path));
        }

        // Create backup if needed
        $backup_path = $file_path . '.backup';
        if (!copy($file_path, $backup_path)) {
            imagedestroy($image);
            return array('success' => false, 'message' => 'Failed to create backup');
        }

        // Get image dimensions for potential resizing
        $width = imagesx($image);
        $height = imagesy($image);
        $max_width = intval($settings['max_width'] ?? 1920);
        $max_height = intval($settings['max_height'] ?? 1080);

        // Resize if image is too large
        if (($settings['resize_larger_images'] ?? true) && ($width > $max_width || $height > $max_height)) {
            $ratio = min($max_width / $width, $max_height / $height);
            $new_width = round($width * $ratio);
            $new_height = round($height * $ratio);

            $resized_image = imagecreatetruecolor($new_width, $new_height);
            imagecopyresampled($resized_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            imagedestroy($image);
            $image = $resized_image;
        }

        // Apply progressive JPEG if enabled
        if ($settings['progressive_jpeg'] ?? true) {
            imageinterlace($image, 1);
        }

        $result = imagejpeg($image, $file_path, $quality);
        imagedestroy($image);

        if (!$result) {
            // Restore backup if optimization failed
            copy($backup_path, $file_path);
            unlink($backup_path);
            return array('success' => false, 'message' => 'Failed to save optimized JPEG');
        }

        // Check if we actually saved space
        $new_size = filesize($file_path);
        $saved_bytes = $original_size - $new_size;

        // If no savings or file got bigger, restore backup
        if ($saved_bytes <= 0) {
            copy($backup_path, $file_path);
            unlink($backup_path);
            return array(
                'success' => true,
                'message' => 'JPEG already optimized (no compression needed)',
                'original_size' => $original_size,
                'optimized_size' => $original_size
            );
        }

        // Clean up backup
        unlink($backup_path);

        return array(
            'success' => true,
            'message' => sprintf('JPEG optimized (quality: %d%%, saved %s)', $quality, size_format($saved_bytes)),
            'original_size' => $original_size,
            'optimized_size' => $new_size
        );
    }

    private function optimize_png($file_path) {
        if (!function_exists('imagecreatefrompng')) {
            return array('success' => false, 'message' => 'GD library not available for PNG');
        }

        // Get original file size
        $original_size = filesize($file_path);

        $image = imagecreatefrompng($file_path);
        if (!$image) {
            return array('success' => false, 'message' => 'Failed to load PNG image: ' . basename($file_path));
        }

        // Create backup
        $backup_path = $file_path . '.backup';
        if (!copy($file_path, $backup_path)) {
            imagedestroy($image);
            return array('success' => false, 'message' => 'Failed to create backup');
        }

        // Get settings
        $settings = get_option('compressly_settings', array());
        $max_width = intval($settings['max_width'] ?? 1920);
        $max_height = intval($settings['max_height'] ?? 1080);

        // Get image dimensions for potential resizing
        $width = imagesx($image);
        $height = imagesy($image);

        // Resize if image is too large
        if (($settings['resize_larger_images'] ?? true) && ($width > $max_width || $height > $max_height)) {
            $ratio = min($max_width / $width, $max_height / $height);
            $new_width = round($width * $ratio);
            $new_height = round($height * $ratio);

            $resized_image = imagecreatetruecolor($new_width, $new_height);

            // Preserve transparency
            imagealphablending($resized_image, false);
            imagesavealpha($resized_image, true);
            $transparent = imagecolorallocatealpha($resized_image, 255, 255, 255, 127);
            imagefill($resized_image, 0, 0, $transparent);

            imagecopyresampled($resized_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            imagedestroy($image);
            $image = $resized_image;
        }

        // Enable alpha blending and save alpha for transparency
        imagealphablending($image, false);
        imagesavealpha($image, true);

        // Use compression level 6 (good balance between size and quality)
        $result = imagepng($image, $file_path, 6);
        imagedestroy($image);

        if (!$result) {
            // Restore backup if optimization failed
            copy($backup_path, $file_path);
            unlink($backup_path);
            return array('success' => false, 'message' => 'Failed to save optimized PNG');
        }

        // Check if we actually saved space
        $new_size = filesize($file_path);
        $saved_bytes = $original_size - $new_size;

        // If no savings or file got bigger, restore backup
        if ($saved_bytes <= 0) {
            copy($backup_path, $file_path);
            unlink($backup_path);
            return array(
                'success' => true,
                'message' => 'PNG already optimized (no compression needed)',
                'original_size' => $original_size,
                'optimized_size' => $original_size
            );
        }

        // Clean up backup
        unlink($backup_path);

        return array(
            'success' => true,
            'message' => sprintf('PNG optimized (compression level: 6, saved %s)', size_format($saved_bytes)),
            'original_size' => $original_size,
            'optimized_size' => $new_size
        );
    }

    private function save_optimization_stats($attachment_id, $original_size, $optimized_size) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'compressly_stats';

        // Check if table exists, if not create it
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                attachment_id bigint(20) NOT NULL,
                original_size bigint(20) NOT NULL,
                optimized_size bigint(20) NOT NULL,
                optimization_date datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY attachment_id (attachment_id)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }

        $wpdb->insert(
            $table_name,
            array(
                'attachment_id' => $attachment_id,
                'original_size' => $original_size,
                'optimized_size' => $optimized_size,
                'optimization_date' => current_time('mysql')
            ),
            array('%d', '%d', '%d', '%s')
        );
    }

    public function handle_reset_optimization() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'compressly_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        global $wpdb;

        // Clear optimization meta data
        $wpdb->delete($wpdb->postmeta, array('meta_key' => '_compressly_optimized'));

        // Clear stats table
        $table_name = $wpdb->prefix . 'compressly_stats';
        $wpdb->query("TRUNCATE TABLE $table_name");

        wp_send_json_success(array('message' => 'Optimization data reset successfully'));
    }
}