<?php
/**
 * Compressly Autoloader
 */

if (!defined('ABSPATH')) {
    exit;
}

class Compressly_Autoloader {
    
    /**
     * Initialize autoloader
     */
    public static function init() {
        spl_autoload_register(array(__CLASS__, 'autoload'));
    }
    
    /**
     * Autoload classes
     */
    public static function autoload($class) {
        $prefix = 'Compressly_';
        $base_dir = COMPRESSLY_PLUGIN_DIR . 'includes/';
        
        // Check if class uses our prefix
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }
        
        // Get relative class name
        $relative_class = substr($class, $len);
        
        // Convert to file path
        $file = $base_dir . 'class-' . str_replace('_', '-', strtolower($relative_class)) . '.php';
        
        // Load file if exists
        if (file_exists($file)) {
            require $file;
        }
    }
}

// Initialize autoloader
Compressly_Autoloader::init();