<?php
/**
 * Plugin Name: Compressly
 * Plugin URI: https://compressly.com
 * Description: AI-powered WordPress media optimization plugin with real-time compression and video optimization.
 * Version: 1.0.0
 * Author: Compressly Team
 * License: GPL v2 or later
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * Text Domain: compressly
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('COMPRESSLY_VERSION', '1.0.0');
define('COMPRESSLY_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('COMPRESSLY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('COMPRESSLY_PLUGIN_FILE', __FILE__);

// Check PHP version
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo sprintf(
            __('Compressly requires PHP 8.0 or higher. You are running PHP %s.', 'compressly'),
            PHP_VERSION
        );
        echo '</p></div>';
    });
    return;
}

// Include the admin class file
$admin_file = COMPRESSLY_PLUGIN_DIR . 'includes/class-compressly-admin.php';
if (file_exists($admin_file)) {
    require_once $admin_file;
} else {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo __('Compressly: Admin class file not found. Please reinstall the plugin.', 'compressly');
        echo '</p></div>';
    });
    return;
}

// Main plugin class
class Compressly {
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init();
    }
    
    private function init() {
        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Initialize admin only if class exists
        if (is_admin() && class_exists('Compressly_Admin')) {
            new Compressly_Admin();
        }
        
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function load_textdomain() {
        load_plugin_textdomain('compressly', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function activate() {
        // Create database tables if needed
        $this->create_tables();
        
        // Set default options
        add_option('compressly_version', COMPRESSLY_VERSION);
        add_option('compressly_settings', array(
            'quality_jpeg' => 85,
            'quality_png' => 6,
            'quality_webp' => 85,
            'max_width' => 2048,
            'max_height' => 2048,
            'auto_optimize' => true
        ));
    }
    
    public function deactivate() {
        // Clean up if needed
    }
    
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'compressly_stats';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) NOT NULL,
            original_size bigint(20) NOT NULL,
            optimized_size bigint(20) NOT NULL,
            optimization_type varchar(50) NOT NULL,
            optimized_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY attachment_id (attachment_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize plugin
Compressly::get_instance();