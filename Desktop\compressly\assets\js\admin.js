(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initDashboard();
        initBulkOptimizer();
        initSettings();
    });
    
    /**
     * Initialize Dashboard
     */
    function initDashboard() {
        // Auto-refresh stats every 30 seconds
        if ($('.compressly-dashboard').length) {
            setInterval(refreshStats, 30000);
        }
    }
    
    /**
     * Initialize Bulk Optimizer
     */
    function initBulkOptimizer() {
        let isOptimizing = false;
        let currentOffset = 0;
        let totalImages = 0;
        let processedImages = 0;
        let totalSaved = 0;
        let totalCompressionRatio = 0;
        
        // Start bulk optimization
        $('#start-bulk-optimization').on('click', function() {
            if (isOptimizing) return;
            
            isOptimizing = true;
            currentOffset = 0;
            processedImages = 0;
            totalSaved = 0;
            totalCompressionRatio = 0;
            totalImages = parseInt($('.total-images').text());
            
            $(this).hide();
            $('#pause-bulk-optimization').show();
            $('.bulk-progress').show();
            $('.log-entries').empty();
            
            processBatch();
        });
        
        // Pause bulk optimization
        $('#pause-bulk-optimization').on('click', function() {
            isOptimizing = false;
            $(this).hide();
            $('#start-bulk-optimization').show().text('Resume Optimization');
        });
        
        /**
         * Process a batch of images
         */
        function processBatch() {
            if (!isOptimizing) return;
            
            $.ajax({
                url: compressly_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'compressly_bulk_optimize',
                    nonce: compressly_ajax.nonce,
                    offset: currentOffset
                },
                success: function(response) {
                    if (response.success && response.data.results) {
                        // Process results
                        response.data.results.forEach(function(result) {
                            processedImages++;
                            
                            if (result.success) {
                                totalSaved += (result.original_size - result.optimized_size);
                                totalCompressionRatio += result.compression_ratio;
                                
                                addLogEntry(result.message, 'success');
                            } else {
                                addLogEntry(result.message, 'error');
                            }
                        });
                        
                        // Update progress
                        updateProgress();
                        
                        // Continue if there are more images
                        if (response.data.has_more && isOptimizing) {
                            currentOffset += 5; // Batch size
                            setTimeout(processBatch, 1000); // Small delay between batches
                        } else {
                            // Optimization complete
                            completeOptimization();
                        }
                    } else {
                        addLogEntry('Error processing batch', 'error');
                        completeOptimization();
                    }
                },
                error: function() {
                    addLogEntry('Network error occurred', 'error');
                    completeOptimization();
                }
            });
        }
        
        /**
         * Update progress display
         */
        function updateProgress() {
            const progress = (processedImages / totalImages) * 100;
            $('.progress-fill').css('width', progress + '%');
            $('.current-progress').text(processedImages);
        }
        
        /**
         * Complete optimization process
         */
        function completeOptimization() {
            isOptimizing = false;
            $('#pause-bulk-optimization').hide();
            $('#start-bulk-optimization').show().text('Start Bulk Optimization');
            
            // Show results
            $('.bulk-results');
        }
    }
    
    // Settings tabs functionality
    function initializeTabs() {
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this).attr('href');
            
            // Update active tab
            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');
            
            // Show target content
            $('.tab-content').removeClass('active');
            $(target).addClass('active');
        });
    }
    
    // Quality slider with live preview
    function initializeQualitySlider() {
        $('.quality-slider').on('input', function() {
            var value = $(this).val();
            $(this).siblings('.quality-value').text(value + '%');
        });
    }
    
    // Bulk optimization functionality
    function initializeBulkOptimizer() {
        var isProcessing = false;
        var currentOffset = 0;
        var totalImages = 0;
        var processedImages = 0;
        var totalSaved = 0;
        var totalCompressionRatio = 0;
        
        $('#start-bulk-optimization').on('click', function() {
            if (isProcessing) return;
            
            isProcessing = true;
            currentOffset = 0;
            processedImages = 0;
            totalSaved = 0;
            totalCompressionRatio = 0;
            totalImages = $('.image-item').length;
            
            $(this).prop('disabled', true).text(compressly_ajax.strings.optimizing);
            $('#bulk-progress').show();
            
            processBatch();
        });
        
        function processBatch() {
            $.ajax({
                url: compressly_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'compressly_bulk_optimize',
                    nonce: compressly_ajax.nonce,
                    offset: currentOffset
                },
                success: function(response) {
                    if (response.success) {
                        // Update progress
                        response.data.results.forEach(function(result, index) {
                            var imageId = $('.image-item').eq(current