<?php
/**
 * Compressly Compression Engine
 */

if (!defined('ABSPATH')) {
    exit;
}

class Compressly_Compression {
    
    /**
     * Settings instance
     */
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = new Compressly_Settings();
        
        // Hook into WordPress upload process
        add_filter('wp_handle_upload', array($this, 'auto_optimize_on_upload'));
        add_filter('wp_generate_attachment_metadata', array($this, 'optimize_thumbnails'), 10, 2);
    }
    
    /**
     * Auto optimize on upload
     */
    public function auto_optimize_on_upload($upload) {
        if (!$this->settings->is_enabled('auto_optimize')) {
            return $upload;
        }
        
        if (!$this->is_supported_image($upload['file'])) {
            return $upload;
        }
        
        $this->optimize_file($upload['file']);
        
        return $upload;
    }
    
    /**
     * Optimize thumbnails
     */
    public function optimize_thumbnails($metadata, $attachment_id) {
        if (!$this->settings->is_enabled('auto_optimize')) {
            return $metadata;
        }
        
        if (!wp_attachment_is_image($attachment_id)) {
            return $metadata;
        }
        
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/' . $metadata['file'];
        
        // Optimize main image
        $this->optimize_file($file_path);
        
        // Optimize thumbnails
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            foreach ($metadata['sizes'] as $size => $size_data) {
                $thumbnail_path = dirname($file_path) . '/' . $size_data['file'];
                if (file_exists($thumbnail_path)) {
                    $this->optimize_file($thumbnail_path);
                }
            }
        }
        
        // Log optimization
        $this->log_optimization($attachment_id, $file_path);
        
        return $metadata;
    }
    
    /**
     * Optimize single image by attachment ID
     */
    public function optimize_image($attachment_id) {
        if (!wp_attachment_is_image($attachment_id)) {
            return array(
                'success' => false,
                'message' => __('Not a valid image attachment', 'compressly')
            );
        }
        
        $file_path = get_attached_file($attachment_id);
        
        if (!file_exists($file_path)) {
            return array(
                'success' => false,
                'message' => __('Image file not found', 'compressly')
            );
        }
        
        $original_size = filesize($file_path);
        
        // Create backup if enabled
        if ($this->settings->is_enabled('backup_originals')) {
            $this->create_backup($file_path);
        }
        
        // Optimize the image
        $result = $this->optimize_file($file_path);
        
        if ($result['success']) {
            $optimized_size = filesize($file_path);
            $compression_ratio = (($original_size - $optimized_size) / $original_size) * 100;
            
            // Log optimization
            $this->log_optimization($attachment_id, $file_path, $original_size, $optimized_size, $compression_ratio);
            
            // Generate WebP version if enabled
            if ($this->settings->is_enabled('webp_conversion')) {
                $this->generate_webp($file_path);
            }
            
            // Generate AVIF version if enabled
            if ($this->settings->is_enabled('avif_conversion')) {
                $this->generate_avif($file_path);
            }
            
            return array(
                'success' => true,
                'message' => sprintf(
                    __('Image optimized successfully! Saved %s (%s%%)', 'compressly'),
                    size_format($original_size - $optimized_size),
                    round($compression_ratio, 1)
                ),
                'original_size' => $original_size,
                'optimized_size' => $optimized_size,
                'compression_ratio' => $compression_ratio
            );
        }
        
        return $result;
    }
    
    /**
     * Optimize file
     */
    private function optimize_file($file_path) {
        $image_type = $this->get_image_type($file_path);
        
        if (!$image_type) {
            return array(
                'success' => false,
                'message' => __('Unsupported image type', 'compressly')
            );
        }
        
        // Resize if needed
        if ($this->settings->is_enabled('resize_larger_images')) {
            $this->resize_image($file_path);
        }
        
        // Compress based on image type
        switch ($image_type) {
            case 'jpeg':
                return $this->compress_jpeg($file_path);
            case 'png':
                return $this->compress_png($file_path);
            case 'gif':
                return $this->compress_gif($file_path);
            default:
                return array(
                    'success' => false,
                    'message' => __('Unsupported image type for compression', 'compressly')
                );
        }
    }
    
    /**
     * Compress JPEG
     */
    private function compress_jpeg($file_path) {
        $image = imagecreatefromjpeg($file_path);
        
        if (!$image) {
            return array(
                'success' => false,
                'message' => __('Failed to load JPEG image', 'compressly')
            );
        }
        
        $quality = $this->settings->get_compression_quality('jpeg');
        
        // Strip metadata if enabled
        if ($this->settings->is_enabled('strip_metadata')) {
            // Metadata is automatically stripped when recreating the image
        }
        
        // Save with compression
        $result = imagejpeg($image, $file_path, $quality);
        imagedestroy($image);
        
        return array(
            'success' => $result,
            'message' => $result ? __('JPEG compressed successfully', 'compressly') : __('Failed to compress JPEG', 'compressly')
        );
    }
    
    /**
     * Compress PNG
     */
    private function compress_png($file_path) {
        $image = imagecreatefrompng($file_path);
        
        if (!$image) {
            return array(
                'success' => false,
                'message' => __('Failed to load PNG image', 'compressly')
            );
        }
        
        // Enable alpha blending and save alpha
        imagealphablending($image, false);
        imagesavealpha($image, true);
        
        // PNG compression level (0-9, where 9 is maximum compression)
        $compression_level = 9;
        
        $result = imagepng($image, $file_path, $compression_level);
        imagedestroy($image);
        
        return array(
            'success' => $result,
            'message' => $result ? __('PNG compressed successfully', 'compressly') : __('Failed to compress PNG', 'compressly')
        );
    }
    
    /**
     * Generate WebP version
     */
    private function generate_webp($file_path) {
        if (!function_exists('imagewebp')) {
            return false;
        }
        
        $image_type = $this->get_image_type($file_path);
        $image = null;
        
        switch ($image_type) {
            case 'jpeg':
                $image = imagecreatefromjpeg($file_path);
                break;
            case 'png':
                $image = imagecreatefrompng($file_path);
                break;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        $webp_path = preg_replace('/\.[^.]+$/', '.webp', $file_path);
        $quality = $this->settings->get_compression_quality('webp');
        
        $result = imagewebp($image, $webp_path, $quality);
        imagedestroy($image);
        
        return $result;
    }
    
    /**
     * Check if image type is supported
     */
    private function is_supported_image($file_path) {
        $supported_types = array('jpeg', 'jpg', 'png', 'gif');
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        
        return in_array($extension, $supported_types);
    }
    
    /**
     * Get image type
     */
    private function get_image_type($file_path) {
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                return 'jpeg';
            case 'png':
                return 'png';
            case 'gif':
                return 'gif';
            default:
                return false;
        }
    }
    
    /**
     * Log optimization
     */
    private function log_optimization($attachment_id, $file_path, $original_size = null, $optimized_size = null, $compression_ratio = null) {
        global $wpdb;
        
        if ($original_size === null) {
            $original_size = filesize($file_path);
        }
        
        if ($optimized_size === null) {
            $optimized_size = filesize($file_path);
        }
        
        if ($compression_ratio === null) {
            $compression_ratio = (($original_size - $optimized_size) / $original_size) * 100;
        }
        
        $table_name = $wpdb->prefix . 'compressly_optimizations';
        
        $wpdb->insert(
            $table_name,
            array(
                'attachment_id' => $attachment_id,
                'original_size' => $original_size,
                'optimized_size' => $optimized_size,
                'compression_ratio' => $compression_ratio,
                'optimization_type' => 'image',
                'status' => 'completed'
            ),
            array('%d', '%d', '%d', '%f', '%s', '%s')
        );
    }
}