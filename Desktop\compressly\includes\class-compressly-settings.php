<?php
/**
 * Compressly Settings Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Compressly_Settings {
    
    /**
     * Default settings
     */
    private $defaults = array(
        'compression_quality' => 85,
        'auto_optimize' => true,
        'webp_conversion' => true,
        'avif_conversion' => false,
        'lazy_loading' => true,
        'cdn_enabled' => false,
        'backup_originals' => true,
        'max_width' => 1920,
        'max_height' => 1080,
        'resize_larger_images' => true,
        'strip_metadata' => true,
        'progressive_jpeg' => true,
        'video_compression' => false,
        'video_quality' => 75,
        'video_max_width' => 1280,
        'video_max_height' => 720,
        'ai_captions' => false,
        'ai_alt_text' => false,
        'performance_monitoring' => true
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('compressly_settings', 'compressly_settings', array(
            'sanitize_callback' => array($this, 'sanitize_settings')
        ));
    }
    
    /**
     * Get setting value
     */
    public function get($key, $default = null) {
        $settings = get_option('compressly_settings', $this->defaults);
        
        if ($default === null && isset($this->defaults[$key])) {
            $default = $this->defaults[$key];
        }
        
        return isset($settings[$key]) ? $settings[$key] : $default;
    }
    
    /**
     * Get all settings
     */
    public function get_all() {
        return wp_parse_args(get_option('compressly_settings', array()), $this->defaults);
    }
    
    /**
     * Update setting
     */
    public function update($key, $value) {
        $settings = $this->get_all();
        $settings[$key] = $value;
        
        return update_option('compressly_settings', $settings);
    }
    
    /**
     * Update multiple settings
     */
    public function update_multiple($new_settings) {
        $settings = $this->get_all();
        $settings = wp_parse_args($new_settings, $settings);
        
        return update_option('compressly_settings', $settings);
    }
    
    /**
     * Reset to defaults
     */
    public function reset_to_defaults() {
        return update_option('compressly_settings', $this->defaults);
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // Compression quality (1-100)
        $sanitized['compression_quality'] = max(1, min(100, intval($input['compression_quality'])));
        
        // Boolean settings
        $boolean_settings = array(
            'auto_optimize', 'webp_conversion', 'avif_conversion', 'lazy_loading',
            'cdn_enabled', 'backup_originals', 'resize_larger_images',
            'strip_metadata', 'progressive_jpeg', 'video_compression',
            'ai_captions', 'ai_alt_text', 'performance_monitoring'
        );
        
        foreach ($boolean_settings as $setting) {
            $sanitized[$setting] = isset($input[$setting]) && $input[$setting];
        }
        
        // Dimension settings
        $sanitized['max_width'] = max(100, min(5000, intval($input['max_width'])));
        $sanitized['max_height'] = max(100, min(5000, intval($input['max_height'])));
        $sanitized['video_max_width'] = max(100, min(4000, intval($input['video_max_width'])));
        $sanitized['video_max_height'] = max(100, min(4000, intval($input['video_max_height'])));
        
        // Video quality (1-100)
        $sanitized['video_quality'] = max(1, min(100, intval($input['video_quality'])));
        
        return $sanitized;
    }
    
    /**
     * Get compression quality for specific image type
     */
    public function get_compression_quality($image_type = 'jpeg') {
        $base_quality = $this->get('compression_quality');
        
        switch ($image_type) {
            case 'png':
                return min(95, $base_quality + 5); // PNG needs higher quality
            case 'webp':
                return max(75, $base_quality - 5); // WebP can be more aggressive
            case 'avif':
                return max(70, $base_quality - 10); // AVIF is very efficient
            default:
                return $base_quality;
        }
    }
    
    /**
     * Check if feature is enabled
     */
    public function is_enabled($feature) {
        return $this->get($feature, false);
    }
    
    /**
     * Get max dimensions
     */
    public function get_max_dimensions() {
        return array(
            'width' => $this->get('max_width'),
            'height' => $this->get('max_height')
        );
    }
    
    /**
     * Get video settings
     */
    public function get_video_settings() {
        return array(
            'enabled' => $this->get('video_compression'),
            'quality' => $this->get('video_quality'),
            'max_width' => $this->get('video_max_width'),
            'max_height' => $this->get('video_max_height')
        );
    }
}